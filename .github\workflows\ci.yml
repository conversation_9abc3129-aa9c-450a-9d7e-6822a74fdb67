name: CI/CD for Gaussian Extractor

on:
  push:
    branches: [main, develop, dev]
  pull_request:
    branches: [main, develop, dev]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y g++ make cmake clang-tidy

      - name: Configure CMake
        run: |
          cmake -S . -B build -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
          if [ $? -ne 0 ]; then
            echo "CMake configuration failed. Please check your CMakeLists.txt for errors."
            exit 1
          fi

      - name: Static Analysis
        run: clang-tidy -p build src/main.cpp src/core/gaussian_extractor.cpp src/core/job_scheduler.cpp

      - name: Build the project
        run: |
          cmake --build build
          if [ $? -ne 0 ]; then
            echo "Build failed. Please check the compilation errors above."
            exit 1
          fi

      - name: Prepare test data
        run: cp tests/data/*.log .

      - name: Run Tests
        id: run_tests
        run: |
          ./build/bin/gaussian_extractor.x -q -f csv
          if ! ls *.csv >/dev/null 2>&1; then
            echo "Test failed: No .csv file generated."
            exit 1
          fi
          # Find the first csv file and check if it's empty
          csv_file=$(ls *.csv | head -n 1)
          if [ ! -s "$csv_file" ]; then
            echo "Test failed: $csv_file is empty."
            exit 1
          fi
          echo "Tests passed."

      - name: Process log and out files
        if: success() || steps.run_tests.conclusion == 'success'
        run: |
          # Run with log files (if any)
          find . -maxdepth 1 -name "*.log" -print0 | xargs -0 -r ./build/bin/gaussian_extractor.x

          # Run with out files (if any)
          find . -maxdepth 1 -name "*.out" -print0 | xargs -0 -r ./build/bin/gaussian_extractor.x -e out

      - name: Upload results artifact
        uses: actions/upload-artifact@v4
        with:
          name: gaussian-results
          path: "*.results"
        if: always()

      - name: Display results
        run: |
          if ls *.results >/dev/null 2>&1; then
            cat *.results
          else
            echo "No results file generated."
          fi
        if: always()
