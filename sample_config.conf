# Gaussian Extractor Configuration File
# Save this as .gaussian_extractor.conf in your home directory or current working directory
#
# Lines starting with # or ; are comments
# Values can be quoted with " or ' if they contain spaces
#

# ==================================================
# general settings
# ==================================================

# Default output file extension to process
output_extension = .log

# Comma-separated list of input file extensions
input_extensions = .com,.gjf,.gau

# Comma-separated list of output file extensions
output_extensions = .log,.out

# Enable quiet mode by default
quiet_mode = false

# Automatically backup files before moving
auto_backup = false

# ==================================================
# extract settings
# ==================================================

# Default temperature in Kelvin
default_temperature = 298.15

# Default concentration in M
default_concentration = 1.0

# Default pressure in atm
default_pressure = 1.0

# Default column to sort by (2-10)
default_sort_column = 2

# Default output format (text/csv)
default_output_format = text

# Use temperature from input files by default
use_input_temp = false

# Apply phase correction by default
phase_correction = true

# ==================================================
# job_checker settings
# ==================================================

# Default suffix for completed jobs directory
done_directory_suffix = done

# Default directory name for error jobs
error_directory_name = errorJobs

# Default directory name for PCM failed jobs
pcm_directory_name = PCMMkU

# Show error details by default
show_error_details = false

# Move related .gau/.chk files with .log files
move_related_files = true

# Create subdirectories for job organization
create_subdirectories = true

# ==================================================
# performance settings
# ==================================================

# Default thread count (number/half/max)
default_threads = half

# Maximum file size to process in MB
max_file_size_mb = 100

# Memory limit in MB (0 = auto)
memory_limit_mb = 0

# Cluster safety mode (auto/on/off)
cluster_safe_mode = auto

# Show progress during processing
progress_reporting = true

# Maximum concurrent file handles
file_handle_limit = 20

# ==================================================
# output settings
# ==================================================

# Template for results filename
results_filename_template = {dirname}.results

# Template for CSV filename
csv_filename_template = {dirname}.csv

# Include metadata in output files
include_metadata = true

# Decimal precision for numerical output
decimal_precision = 6

# Use scientific notation for small numbers
scientific_notation = false

# Include timestamps in output
include_timestamps = true

# End of configuration file
